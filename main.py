import pandas as pd
import numpy as np
from graph import *

# Lê o arquivo CSV
df = pd.read_csv('netflix_amazon_disney_titles.csv', usecols=['title', 'director', 'cast'])
df = df.head(100)
print(df.head())

teste = pd.read_csv('teste_degree_centrality.csv', usecols=['Origem', 'Destino', 'Peso'])
graph_teste_d = Graph_directed()
graph_teste_u = Graph_undirected()


# Cria uma instância do grafo direcionado
graph_d = Graph_directed()
graph_u = Graph_undirected()



# #Questão 1:
# # Constrói o grafo com os dados
# graph_d, graph_u = construct_graph(graph_d, graph_u, df)

# #Após a construção de cada grafo, retorne a quantidade de vértices e arestas.
# graph_d.return_vertex_edges()
# graph_u.return_vertex_edges()

# save_graph_csv(graph_d)
# save_graph_csv(graph_u)

# gd_transpose = graph_d.transpose()
# save_graph_csv(gd_transpose, True)


#Questao 4 - Testando degree centrality
print("=== TESTANDO DEGREE CENTRALITY ===")
print("Dados do arquivo teste:", teste.head())

# Popula o grafo de teste com os dados do CSV teste_degree_centrality.csv
for _, row in teste.iterrows():
    origem = row['Origem']
    destino = row['Destino']
    peso = row['Peso']

    # Adiciona a aresta (que automaticamente adiciona os vértices se não existirem)
    graph_teste_d.add_edge(origem, destino, peso)

print("\nVértices no grafo de teste:", graph_teste_d.vertices)
print("Arestas no grafo:", dict(graph_teste_d.body))

# Testa a centralidade para cada vértice
print("\n=== CENTRALIDADE DE GRAU ===")
for vertice in graph_teste_d.vertices:
    centralidade = graph_teste_d.degree_centrality(vertice)
    print(f"Centralidade de '{vertice}': {centralidade:.4f}")