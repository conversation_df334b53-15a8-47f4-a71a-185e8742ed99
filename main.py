import pandas as pd
import numpy as np
from graph import *

# Lê o arquivo CSV
df = pd.read_csv('netflix_amazon_disney_titles.csv', usecols=['title', 'director', 'cast'])
df = df.head(100)
print(df.head())

teste = pd.read_csv('teste_degree_centrality.csv', usecols=['Origem', 'Destino', 'Peso'])
graph_teste_d = Graph_directed()
graph_teste_u = Graph_undirected()


# Cria uma instância do grafo direcionado
graph_d = Graph_directed()
graph_u = Graph_undirected()



#Questão 1:
# Constrói o grafo com os dados
graph_d, graph_u = construct_graph(graph_d, graph_u, df)

#Após a construção de cada grafo, retorne a quantidade de vértices e arestas.
graph_d.return_vertex_edges()
graph_u.return_vertex_edges()

save_graph_csv(graph_d)
save_graph_csv(graph_u)

gd_transpose = graph_d.transpose()
save_graph_csv(gd_transpose, True)


#Questao 4 - Análise dos 10 diretores mais influentes
print("\n=== ANÁLISE DOS 10 DIRETORES MAIS INFLUENTES ===")

print("🎯 Calculando centralidade de grau para todos os vértices...")
results_all = graph_d.analyze_degree_centrality()


grafo_direcionado_df = pd.read_csv('graph_Graph_directed.csv')
diretores = set(grafo_direcionado_df['Destino'].unique())
print(f"📊 Total de diretores identificados: {len(diretores)}")

# Filtra apenas os diretores dos resultados
diretores_centrality = {diretor: results_all[diretor] for diretor in diretores if diretor in results_all}

# Ordena por centralidade decrescente e pega os top 10
top_10_diretores = sorted(diretores_centrality.items(), key=lambda x: x[1], reverse=True)[:10]

print("\n🏆 TOP 10 DIRETORES MAIS INFLUENTES (por Centralidade de Grau):")
print("=" * 70)
print(f"{'Rank':<4} {'Diretor':<30} {'Centralidade':<15} {'In-Degree':<10}")
print("-" * 70)

for i, (diretor, centralidade) in enumerate(top_10_diretores, 1):
    in_degree = graph_d.indegree(diretor)
    print(f"{i:<4} {diretor:<30} {centralidade:<15.4f} {in_degree:<10}")

# Análise específica do diretor mais influente
if top_10_diretores:
    diretor_top = top_10_diretores[0][0]
    print(f"\n🔍 ANÁLISE DETALHADA DO DIRETOR MAIS INFLUENTE:")
    graph_d.analyze_degree_centrality(diretor_top, show_details=True)

print("\n" + "=" * 100)
print("=== ANÁLISE DOS 10 ATORES/ATRIZES MAIS INFLUENTES (GRAFO NÃO-DIRECIONADO) ===")
print("=" * 100)

# Carrega o CSV do grafo não-direcionado
print("📂 Carregando grafo não-direcionado do arquivo CSV...")
grafo_nao_direcionado_df = pd.read_csv('graph_Graph_undirected.csv')

# Reconstrói o grafo não-direcionado usando as classes do graph.py
graph_u_completo = Graph_undirected()

print("🔧 Reconstruindo grafo não-direcionado...")
for _, row in grafo_nao_direcionado_df.iterrows():
    origem = row['Origem']
    destino = row['Destino']
    peso = row['Peso']
    graph_u_completo.add_edge(origem, destino, peso)

print(f"✅ Grafo carregado: {graph_u_completo.order} vértices, {graph_u_completo.size} arestas")

# Calcula centralidade para todos os vértices (só mostra estatísticas, não todos os vértices)
print("\n🎯 Calculando centralidade de grau para todos os atores...")
results_all_undirected = {}
for vertice in graph_u_completo.vertices:
    results_all_undirected[vertice] = graph_u_completo.degree_centrality(vertice)

# Todos os vértices são atores no grafo não-direcionado
print(f"🎭 Total de atores identificados: {len(results_all_undirected)}")

# Ordena por centralidade decrescente e pega os top 10
top_10_atores = sorted(results_all_undirected.items(), key=lambda x: x[1], reverse=True)[:10]

print("\n🏆 TOP 10 ATORES/ATRIZES MAIS INFLUENTES (por Centralidade de Grau):")
print("=" * 70)
print(f"{'Rank':<4} {'Ator/Atriz':<30} {'Centralidade':<15} {'Grau':<10}")
print("-" * 70)

for i, (ator, centralidade) in enumerate(top_10_atores, 1):
    grau = graph_u_completo.degree(ator)
    print(f"{i:<4} {ator:<30} {centralidade:<15.4f} {grau:<10}")

print("\n" + "=" * 70)
print("📊 O QUE ESSA MÉTRICA REPRESENTA NO CONTEXTO:")
print("=" * 70)
print("• Centralidade de Grau = Grau / (n-1)")
print("• Grau = Número de atores diferentes com quem trabalhou junto")
print("• Maior centralidade = Ator mais 'conectado' na rede de colaborações")
print("• Representa a 'influência' em termos de diversidade de parcerias")
print("• Atores com alta centralidade são 'hubs' na rede cinematográfica")
print("• Indica capacidade de trabalhar com muitos colegas diferentes")

# Análise específica do ator mais influente
if top_10_atores:
    ator_top = top_10_atores[0][0]
    print(f"\n🔍 ANÁLISE DETALHADA DO ATOR/ATRIZ MAIS INFLUENTE:")
    graph_u_completo.analyze_degree_centrality(ator_top, show_details=True)



